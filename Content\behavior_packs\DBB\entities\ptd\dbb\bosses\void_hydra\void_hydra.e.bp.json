{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_dbb:void_hydra", "is_spawnable": true, "is_summonable": true, "properties": {"ptd_dbb:spawning": {"type": "bool", "client_sync": true, "default": true}, "ptd_dbb:dead": {"type": "bool", "client_sync": true, "default": false}, "ptd_dbb:death_timer": {"type": "int", "client_sync": true, "range": [0, 150], "default": 0}, "ptd_dbb:attack": {"type": "enum", "client_sync": true, "default": "none", "values": ["none", "right_atomic_cross", "right_atomic", "right_vacuum", "right_summon", "mid_atomic", "mid_meteor", "mid_singularity", "left_atomic_cross", "left_atomic", "left_railgun", "left_missile", "left_shout"]}, "ptd_dbb:attack_timer": {"type": "int", "client_sync": true, "range": [0, 300], "default": 0}, "ptd_dbb:attack_cooldown": {"type": "int", "client_sync": false, "range": [0, 100], "default": 0}, "ptd_dbb:cooling_down": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"ptd_dbb:spawning": {"minecraft:is_collidable": {}, "minecraft:timer": {"time": 14, "looping": false, "time_down_event": {"event": "ptd_dbb:on_spawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}}, "ptd_dbb:default": {"minecraft:is_collidable": {}, "minecraft:movement": {"value": 0.3}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "can_pass_doors": true, "can_open_doors": false, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.look_at_player": {"priority": 9, "look_distance": 32, "probability": 0.8}, "minecraft:behavior.random_look_around": {"priority": 10}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "must_see_forget_duration": 3.0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild", "operator": "!="}]}, "max_dist": 64}]}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1}}, "ptd_dbb:cooldown": {"minecraft:timer": {"time": 1.0, "looping": false, "time_down_event": {"event": "ptd_dbb:cooldown_complete", "target": "self"}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ptd_dbb:spawning"]}}, "ptd_dbb:on_spawn": {"set_property": {"ptd_dbb:spawning": false}, "add": {"component_groups": ["ptd_dbb:default"]}, "remove": {"component_groups": ["ptd_dbb:spawning"]}}, "ptd_dbb:dead": {"set_property": {"ptd_dbb:dead": true}, "queue_command": {"command": ["scriptevent ptd_dbb:void_hydra_death"]}}, "ptd_dbb:right_atomic_cross_attack": {"set_property": {"ptd_dbb:attack": "right_atomic_cross"}}, "ptd_dbb:right_atomic_attack": {"set_property": {"ptd_dbb:attack": "right_atomic"}}, "ptd_dbb:right_vacuum_attack": {"set_property": {"ptd_dbb:attack": "right_vacuum"}}, "ptd_dbb:right_summon_attack": {"set_property": {"ptd_dbb:attack": "right_summon"}}, "ptd_dbb:mid_atomic_attack": {"set_property": {"ptd_dbb:attack": "mid_atomic"}}, "ptd_dbb:mid_meteor_attack": {"set_property": {"ptd_dbb:attack": "mid_meteor"}}, "ptd_dbb:mid_singularity_attack": {"set_property": {"ptd_dbb:attack": "mid_singularity"}}, "ptd_dbb:left_atomic_cross_attack": {"set_property": {"ptd_dbb:attack": "left_atomic_cross"}}, "ptd_dbb:left_atomic_attack": {"set_property": {"ptd_dbb:attack": "left_atomic"}}, "ptd_dbb:left_railgun_attack": {"set_property": {"ptd_dbb:attack": "left_railgun"}}, "ptd_dbb:left_missile_attack": {"set_property": {"ptd_dbb:attack": "left_missile"}}, "ptd_dbb:left_shout_attack": {"set_property": {"ptd_dbb:attack": "left_shout"}}, "ptd_dbb:reset_attack": {"sequence": [{"set_property": {"ptd_dbb:attack": "none", "ptd_dbb:cooling_down": true}}, {"add": {"component_groups": ["ptd_dbb:cooldown"]}}]}, "ptd_dbb:cooldown_complete": {"sequence": [{"set_property": {"ptd_dbb:cooling_down": false}}, {"remove": {"component_groups": ["ptd_dbb:cooldown"]}}]}}, "components": {"minecraft:type_family": {"family": ["void_hydra", "boss"]}, "minecraft:collision_box": {"width": 4, "height": 6}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 4.5, "height": 6.5, "pivot": [0, 3.25, 0]}]}, "minecraft:health": {"value": 1500, "max": 1500}, "minecraft:boss": {"hud_range": 32, "name": "Void Hydra", "should_darken_sky": false}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 10}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:physics": {}}}}